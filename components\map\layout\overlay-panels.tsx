"use client"

import { AssetPanel } from "@/components/map/asset-panel"
import { RoutePanel } from "@/components/map/route-panel"
import { PartnerPanel } from "@/components/map/partner-panel"
import type { Partner, Route, LiveAsset } from "@/types/map-types"

interface OverlayPanelsProps {
  selectedAsset: LiveAsset | null
  selectedRoute: Route | null
  selectedPartner: Partner | null
  filteredAssets: LiveAsset[]
  onCloseAsset: () => void
  onCloseRoute: () => void
  onClosePartner: () => void
  onSelectAsset: (asset: LiveAsset) => void
}

export function OverlayPanels({
  selectedAsset,
  selectedRoute,
  selectedPartner,
  filteredAssets,
  onCloseAsset,
  onCloseRoute,
  onClosePartner,
  onSelectAsset,
}: OverlayPanelsProps) {
  return (
    <>
      {/* Asset Panel */}
      {selectedAsset && (
        <div className="fixed inset-0 z-50 bg-black/20 backdrop-blur-sm flex items-center justify-center">
          <div className="w-96 max-h-[80vh] overflow-y-auto">
            <AssetPanel selectedAsset={selectedAsset} onClose={onCloseAsset} />
          </div>
        </div>
      )}

      {/* Route Panel */}
      {selectedRoute && !selectedAsset && (
        <div className="fixed inset-0 z-50 bg-black/20 backdrop-blur-sm flex items-center justify-center">
          <div className="w-96 max-h-[80vh] overflow-y-auto">
            <RoutePanel
              selectedRoute={selectedRoute}
              filteredAssets={filteredAssets}
              onClose={onCloseRoute}
              onSelectAsset={onSelectAsset}
            />
          </div>
        </div>
      )}

      {/* Partner Panel */}
      {selectedPartner && !selectedRoute && !selectedAsset && (
        <div className="fixed inset-0 z-50 bg-black/20 backdrop-blur-sm flex items-center justify-center">
          <div className="w-96 max-h-[80vh] overflow-y-auto">
            <PartnerPanel
              selectedPartner={selectedPartner}
              filteredAssets={filteredAssets}
              onClose={onClosePartner}
            />
          </div>
        </div>
      )}
    </>
  )
}
