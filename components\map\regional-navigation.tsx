"use client"

import { But<PERSON> } from "@/components/ui/button"
import { serviceTypes, regions } from "@/data/map-data"
import type { Region, RegionalStats } from "@/types/map-types"
import { X } from "lucide-react"
import { ChevronDown } from "lucide-react"

// Add new props to the interface
interface RegionalNavigationProps {
  selectedRegion: string | null
  activeServices: Set<string>
  regionalStats: RegionalStats[]
  isMinimized: boolean
  onRegionClick: (region: Region) => void
  onToggleMinimized: () => void
  hasSelectedPanels: boolean
}

export function RegionalNavigation({
  selectedRegion,
  activeServices,
  regionalStats,
  isMinimized,
  onRegionClick,
  onToggleMinimized,
  hasSelectedPanels,
}: RegionalNavigationProps) {
  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-slate-900 font-semibold">Regional Navigation</h3>
        <Button variant="ghost" size="sm" onClick={onToggleMinimized} className="text-slate-600 hover:text-slate-900">
          {isMinimized ? <ChevronDown className="w-4 h-4" /> : <X className="w-4 h-4" />}
        </Button>
      </div>

      {!isMinimized && (
        <div className="space-y-2">
          {regions.map((region) => {
            const stats = regionalStats.find((s) => s.regionId === region.id)
            const activeRegionServices = region.services.filter((s) => activeServices.has(s))
            return (
              <Button
                key={region.id}
                variant="ghost"
                className={`w-full justify-start text-left h-auto p-3 ${
                  selectedRegion === region.id ? "bg-[#52AAA3]/20 border border-[#52AAA3]/50" : "hover:bg-slate-100"
                }`}
                onClick={() => onRegionClick(region)}
              >
                <div className="flex flex-col items-start gap-1 w-full">
                  <div className="flex justify-between w-full">
                    <span className="text-slate-900 font-medium">{region.name}</span>
                    {stats && <span className="text-[#52AAA3] text-xs">{stats.activeShipments} active</span>}
                  </div>
                  <div className="flex gap-1">
                    {region.services.map((serviceId) => {
                      const service = serviceTypes.find((s) => s.id === serviceId)
                      if (!service) return null
                      return (
                        <div
                          key={serviceId}
                          className="w-2 h-2 rounded-full"
                          style={{
                            backgroundColor: service.color,
                            opacity: activeServices.has(serviceId) ? 1 : 0.3,
                          }}
                        />
                      )
                    })}
                  </div>
                  <div className="text-xs text-slate-600">
                    {activeRegionServices.length} of {region.services.length} services active
                    {stats && <span className="ml-2">• {stats.efficiency}% efficiency</span>}
                  </div>
                </div>
              </Button>
            )
          })}
        </div>
      )}

      {isMinimized && (
        <div className="flex items-center gap-2">
          <div className="text-sm text-slate-700">
            {regions.filter((region) => region.services.some((s) => activeServices.has(s))).length} regions active
          </div>
        </div>
      )}
    </div>
  )
}
