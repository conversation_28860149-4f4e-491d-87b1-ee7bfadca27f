"use client"

import { MapPin, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import type { LiveAsset } from "@/types/map-types"

interface AssetPanelProps {
  selectedAsset: LiveAsset
  onClose: () => void
}

export function AssetPanel({ selectedAsset, onClose }: AssetPanelProps) {
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "on-time":
        return "bg-teal-100 text-teal-800 border-teal-300"
      case "delayed":
        return "bg-slate-100 text-slate-800 border-slate-300"
      case "critical":
        return "bg-red-100 text-red-800 border-red-300"
      default:
        return "bg-gray-100 text-gray-800 border-gray-300"
    }
  }

  return (
    <div className="fixed inset-y-0 left-0 w-96 z-30 transform transition-transform duration-300 ease-in-out">
      <Card className="h-full glassmorphism border-r border-slate-200 rounded-none overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center gap-3">
              <MapPin className="w-8 h-8 text-[#52AAA3]" />
              <div>
                <h2 className="text-xl font-bold text-slate-900">Live Asset Tracking</h2>
                <p className="text-slate-600 text-sm">{selectedAsset.cargo}</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-slate-600 hover:text-slate-900 hover:bg-slate-100"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Status Badges */}
          <div className="flex gap-2 mb-6">
            <Badge className={getStatusBadgeColor(selectedAsset.status)}>{selectedAsset.status.toUpperCase()}</Badge>
            <Badge className="bg-blue-100 text-blue-800 border-blue-300">{selectedAsset.type.toUpperCase()}</Badge>
          </div>

          {/* Progress */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <span className="text-slate-900 font-medium">Journey Progress</span>
              <span className="text-[#52AAA3]">{Math.round(selectedAsset.progress * 100)}%</span>
            </div>
            <Progress value={selectedAsset.progress * 100} className="h-2" />
          </div>

          <Separator className="bg-slate-200 mb-6" />

          {/* Asset Details */}
          <div className="space-y-4 mb-6">
            <div className="flex justify-between">
              <span className="text-slate-600">Origin</span>
              <span className="text-slate-900">{selectedAsset.origin}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-600">Destination</span>
              <span className="text-slate-900">{selectedAsset.destination}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-600">Cargo Value</span>
              <span className="text-slate-900">${selectedAsset.value.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-600">ETA</span>
              <span className="text-slate-900">{new Date(selectedAsset.eta).toLocaleDateString()}</span>
            </div>
          </div>

          {/* Current Location */}
          <div className="mb-6">
            <h3 className="text-slate-900 font-semibold mb-2">Current Location</h3>
            <div className="bg-slate-50 rounded-lg p-3 border border-slate-200">
              <p className="text-slate-600 text-sm">Lat: {selectedAsset.currentLocation[1].toFixed(4)}°</p>
              <p className="text-slate-600 text-sm">Lng: {selectedAsset.currentLocation[0].toFixed(4)}°</p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button className="flex-1 bg-[#52AAA3] hover:bg-[#52AAA3]/90 text-white">Track Asset</Button>
            <Button variant="outline" className="border-[#52AAA3] text-[#52AAA3] hover:bg-[#52AAA3]/10">
              Details
            </Button>
          </div>
        </div>
      </Card>
    </div>
  )
}
