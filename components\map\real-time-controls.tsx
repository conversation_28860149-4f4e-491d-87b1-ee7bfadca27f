"use client"

import { Activity, Play, Pause, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Separator } from "@/components/ui/separator"
import { serviceTypes } from "@/data/map-data"
import type { RealTimeData } from "@/types/map-types"

interface RealTimeControlsProps {
  isRealTimeEnabled: boolean
  showAssets: boolean
  showHeatmap: boolean
  showEvents: boolean
  showRegionalStats: boolean
  updateInterval: number[]
  activeServices: Set<string>
  realTimeData: RealTimeData
  isMinimized: boolean
  onToggleRealTime: () => void
  onToggleAssets: (value: boolean) => void
  onToggleHeatmap: (value: boolean) => void
  onToggleEvents: (value: boolean) => void
  onToggleRegionalStats: (value: boolean) => void
  onUpdateIntervalChange: (value: number[]) => void
  onToggleService: (serviceId: string) => void
  onToggleAllServices: () => void
  onToggleMinimized: () => void
}

export function RealTimeControls({
  isRealTimeEnabled,
  showAssets,
  showHeatmap,
  showEvents,
  showRegionalStats,
  updateInterval,
  activeServices,
  realTimeData,
  isMinimized,
  onToggleRealTime,
  onToggleAssets,
  onToggleHeatmap,
  onToggleEvents,
  onToggleRegionalStats,
  onUpdateIntervalChange,
  onToggleService,
  onToggleAllServices,
  onToggleMinimized,
}: RealTimeControlsProps) {
  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className={`text-slate-900 font-semibold flex items-center gap-2 ${isMinimized ? "hidden" : ""}`}>
          <Activity className="w-4 h-4" />
          Real-Time Controls
        </h3>
        <div className="flex items-center gap-2">
          {!isMinimized && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleRealTime}
              className={`text-xs ${isRealTimeEnabled ? "text-green-600" : "text-red-600"}`}
            >
              {isRealTimeEnabled ? <Play className="w-3 h-3" /> : <Pause className="w-3 h-3" />}
              {isRealTimeEnabled ? "Live" : "Paused"}
            </Button>
          )}
          <Button variant="ghost" size="sm" onClick={onToggleMinimized} className="text-slate-600 hover:text-slate-900">
            <X className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Show minimized view */}
      {isMinimized ? (
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <Activity className="w-4 h-4 text-[#52AAA3]" />
            {isRealTimeEnabled ? (
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            ) : (
              <div className="w-2 h-2 bg-red-500 rounded-full" />
            )}
          </div>
          <div className="text-xs text-slate-600">{realTimeData.globalMetrics.totalShipments} assets</div>
        </div>
      ) : (
        <>
          {/* Global Metrics */}
          <div className="mb-4">
            <h4 className="text-slate-900 font-medium text-sm mb-2">Global Metrics</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="bg-slate-50 rounded p-2 border border-slate-200">
                <p className="text-slate-600">Active Assets</p>
                <p className="text-[#52AAA3] font-bold">{realTimeData.globalMetrics.totalShipments}</p>
              </div>
              <div className="bg-slate-50 rounded p-2 border border-slate-200">
                <p className="text-slate-600">Total Value</p>
                <p className="text-[#52AAA3] font-bold">
                  ${Math.round(realTimeData.globalMetrics.totalValue / 1000000)}M
                </p>
              </div>
              <div className="bg-slate-50 rounded p-2 border border-slate-200">
                <p className="text-slate-600">Avg Delay</p>
                <p className="text-[#52AAA3] font-bold">{realTimeData.globalMetrics.averageDelay.toFixed(1)}h</p>
              </div>
              <div className="bg-slate-50 rounded p-2 border border-slate-200">
                <p className="text-slate-600">System Health</p>
                <p className="text-[#52AAA3] font-bold">{realTimeData.globalMetrics.systemHealth}%</p>
              </div>
            </div>
          </div>

          <Separator className="bg-slate-200 mb-4" />

          {/* Layer Controls */}
          <div className="space-y-3 mb-4">
            <div className="flex items-center justify-between">
              <span className="text-slate-900 text-sm">Live Assets</span>
              <Switch
                checked={showAssets}
                onCheckedChange={onToggleAssets}
                className="data-[state=checked]:bg-[#52AAA3]"
              />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-slate-900 text-sm">Regional Heatmap</span>
              <Switch
                checked={showHeatmap}
                onCheckedChange={onToggleHeatmap}
                className="data-[state=checked]:bg-[#52AAA3]"
              />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-slate-900 text-sm">Live Events</span>
              <Switch
                checked={showEvents}
                onCheckedChange={onToggleEvents}
                className="data-[state=checked]:bg-[#52AAA3]"
              />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-slate-900 text-sm">Regional Stats</span>
              <Switch
                checked={showRegionalStats}
                onCheckedChange={onToggleRegionalStats}
                className="data-[state=checked]:bg-[#52AAA3]"
              />
            </div>
          </div>

          <Separator className="bg-slate-200 mb-4" />

          {/* Update Frequency */}
          <div className="space-y-2 mb-4">
            <p className="text-slate-600 text-xs">Update Frequency</p>
            <Slider
              value={updateInterval}
              onValueChange={onUpdateIntervalChange}
              max={10000}
              min={500}
              step={500}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-slate-600">
              <span>0.5s</span>
              <span>{updateInterval[0] / 1000}s</span>
              <span>10s</span>
            </div>
          </div>

          {/* Service Filters */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="text-slate-900 font-medium text-sm">Service Filters</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleAllServices}
                className="text-[#52AAA3] hover:bg-[#52AAA3]/10 text-xs"
              >
                {activeServices.size === serviceTypes.length ? "Clear" : "All"}
              </Button>
            </div>
            <div className="space-y-1">
              {serviceTypes.map((service) => {
                const Icon = service.icon
                const isActive = activeServices.has(service.id)
                return (
                  <div
                    key={service.id}
                    className={`flex items-center gap-2 p-2 rounded cursor-pointer transition-all border ${
                      isActive ? "bg-slate-50 border-slate-200" : "bg-slate-25 border-slate-100 opacity-60"
                    }`}
                    onClick={() => onToggleService(service.id)}
                  >
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: service.color, opacity: isActive ? 1 : 0.5 }}
                    />
                    <Icon className="w-3 h-3 text-slate-600" />
                    <span className="text-slate-900 text-xs flex-1">{service.name}</span>
                    <Switch
                      checked={isActive}
                      onCheckedChange={() => onToggleService(service.id)}
                      className="data-[state=checked]:bg-[#52AAA3] scale-75"
                    />
                  </div>
                )
              })}
            </div>
          </div>

          {/* Last Update */}
          <div className="mt-4 pt-3 border-t border-slate-200">
            <p className="text-slate-600 text-xs">Last update: {realTimeData.lastUpdate.toLocaleTimeString()}</p>
          </div>
        </>
      )}
    </div>
  )
}
