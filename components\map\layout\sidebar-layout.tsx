"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { RegionalNavigation } from "@/components/map/regional-navigation"
import { LiveEventsPanel } from "@/components/map/live-events-panel"
import { StatusPanel } from "@/components/map/status-panel"
import type { RealTimeData, LiveAsset, Region } from "@/types/map-types"

interface SidebarLayoutProps {
  // Left sidebar state
  isLeftSidebarVisible: boolean
  isRegionalNavMinimized: boolean
  isLiveEventsMinimized: boolean
  isStatusPanelMinimized: boolean
  
  // Data
  selectedRegion: string | null
  activeServices: Set<string>
  realTimeData: RealTimeData
  filteredAssets: LiveAsset[]
  routeIntensityFilter: string[]
  currentZoom: number
  isRealTimeEnabled: boolean
  showRoutes: boolean
  showAssets: boolean
  showEvents: boolean
  
  // Handlers
  onRegionClick: (region: Region) => void
  setIsRegionalNavMinimized: (value: boolean) => void
  setIsLiveEventsMinimized: (value: boolean) => void
  setIsStatusPanelMinimized: (value: boolean) => void
}

export function SidebarLayout({
  isLeftSidebarVisible,
  isRegionalNavMinimized,
  isLiveEventsMinimized,
  isStatusPanelMinimized,
  selectedRegion,
  activeServices,
  realTimeData,
  filteredAssets,
  routeIntensityFilter,
  currentZoom,
  isRealTimeEnabled,
  showRoutes,
  showAssets,
  showEvents,
  onRegionClick,
  setIsRegionalNavMinimized,
  setIsLiveEventsMinimized,
  setIsStatusPanelMinimized,
}: SidebarLayoutProps) {
  if (!isLeftSidebarVisible) {
    return (
      <div className="fixed left-4 top-1/2 transform -translate-y-1/2 z-30">
        <div className="bg-white/90 backdrop-blur-sm border border-slate-200 rounded-lg shadow-lg p-2">
          <div className="flex flex-col gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsRegionalNavMinimized(false)}
              className="justify-start text-xs text-slate-600 hover:text-slate-900 w-20"
            >
              📍
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsLiveEventsMinimized(false)}
              className="justify-start text-xs text-slate-600 hover:text-slate-900 w-20"
            >
              🚨
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsStatusPanelMinimized(false)}
              className="justify-start text-xs text-slate-600 hover:text-slate-900 w-20"
            >
              📊
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white/80 backdrop-blur-sm border-r border-slate-200 overflow-y-auto max-h-[calc(100vh-120px)]">
      <div className="space-y-0">
        {/* Regional Navigation */}
        {!isRegionalNavMinimized && (
          <div className="border-b border-slate-200">
            <RegionalNavigation
              selectedRegion={selectedRegion}
              activeServices={activeServices}
              regionalStats={realTimeData.regionalStats}
              isMinimized={isRegionalNavMinimized}
              onRegionClick={onRegionClick}
              onToggleMinimized={() => setIsRegionalNavMinimized(!isRegionalNavMinimized)}
              hasSelectedPanels={false}
            />
          </div>
        )}

        {/* Live Events Panel */}
        {!isLiveEventsMinimized && (
          <div className="border-b border-slate-200">
            <LiveEventsPanel
              events={realTimeData.events}
              showEvents={showEvents}
              isMinimized={isLiveEventsMinimized}
              onToggleMinimized={() => setIsLiveEventsMinimized(!isLiveEventsMinimized)}
            />
          </div>
        )}

        {/* Status Panel */}
        {!isStatusPanelMinimized && (
          <div>
            <StatusPanel
              activeServices={activeServices}
              showRoutes={showRoutes}
              showAssets={showAssets}
              filteredAssets={filteredAssets}
              routeIntensityFilter={routeIntensityFilter}
              currentZoom={currentZoom}
              isRealTimeEnabled={isRealTimeEnabled}
              isMinimized={isStatusPanelMinimized}
              onToggleMinimized={() => setIsStatusPanelMinimized(!isStatusPanelMinimized)}
              hasSelectedPanels={false}
            />
          </div>
        )}

        {/* Minimized Left Sidebar Controls */}
        {(isRegionalNavMinimized || isLiveEventsMinimized || isStatusPanelMinimized) && (
          <div className="p-2 border-b border-slate-200 bg-slate-50">
            <div className="flex flex-col gap-1">
              {isRegionalNavMinimized && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsRegionalNavMinimized(false)}
                  className="justify-start text-xs text-slate-600 hover:text-slate-900"
                >
                  📍 Regions
                </Button>
              )}
              {isLiveEventsMinimized && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsLiveEventsMinimized(false)}
                  className="justify-start text-xs text-slate-600 hover:text-slate-900"
                >
                  🚨 Events ({realTimeData.events.length})
                </Button>
              )}
              {isStatusPanelMinimized && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsStatusPanelMinimized(false)}
                  className="justify-start text-xs text-slate-600 hover:text-slate-900"
                >
                  📊 Status
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
