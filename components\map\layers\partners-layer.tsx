"use client"

import { useEffect } from "react"
import * as d3 from "d3"
import type { Partner } from "@/types/map-types"

interface PartnersLayerProps {
  svg: d3.Selection<SVGSVGElement, unknown, null, undefined>
  currentZoom: number
  filteredPartners: Partner[]
  hoveredPartner: string | null
  selectedPartner: Partner | null
  animationSpeed: number[]
  projection: d3.GeoProjection
  onPartnerClick: (partner: Partner) => void
  onPartnerHover: (partnerId: string | null) => void
}

export function usePartnersLayer({
  svg,
  currentZoom,
  filteredPartners,
  hoveredPartner,
  selectedPartner,
  animationSpeed,
  projection,
  onPartnerClick,
  onPartnerHover,
}: PartnersLayerProps) {
  useEffect(() => {
    if (!svg || currentZoom < 1.5) return

    const partnerGroup = svg.append("g").attr("class", "partners")

    filteredPartners.forEach((partner) => {
      const [x, y] = projection(partner.coordinates) || [0, 0]

      const marker = partnerGroup
        .append("g")
        .attr("class", `partner-marker partner-${partner.id}`)
        .style("cursor", "pointer")
        .on("click", () => onPartnerClick(partner))
        .on("mouseenter", () => onPartnerHover(partner.id))
        .on("mouseleave", () => onPartnerHover(null))

      // Pulsing background
      if (hoveredPartner === partner.id || selectedPartner?.id === partner.id) {
        marker
          .append("circle")
          .attr("cx", x)
          .attr("cy", y)
          .attr("r", 20)
          .style("fill", partner.streamlnkIntegrated ? "#52AAA3" : "#eab308")
          .style("opacity", 0.3)
          .style("animation", `pulse ${2 / animationSpeed[0]}s infinite`)
      }

      // Main marker
      const markerSize = partner.tier === "premium" ? 12 : partner.tier === "standard" ? 10 : 8
      marker
        .append("circle")
        .attr("cx", x)
        .attr("cy", y)
        .attr("r", markerSize)
        .style("fill", partner.streamlnkIntegrated ? "#52AAA3" : "#eab308")
        .style("stroke", "#fff")
        .style("stroke-width", "2px")
        .style("filter", hoveredPartner === partner.id ? "brightness(1.3)" : "none")
        .style("transition", "all 0.3s ease")

      // Tier indicator
      const tierColor = partner.tier === "premium" ? "#10b981" : partner.tier === "standard" ? "#3b82f6" : "#6b7280"
      marker
        .append("circle")
        .attr("cx", x + 8)
        .attr("cy", y - 8)
        .attr("r", 4)
        .style("fill", tierColor)
        .style("stroke", "#fff")
        .style("stroke-width", "1px")

      // Partner name label
      if (hoveredPartner === partner.id || selectedPartner?.id === partner.id) {
        marker
          .append("rect")
          .attr("x", x - 40)
          .attr("y", y - 35)
          .attr("width", 80)
          .attr("height", 20)
          .attr("rx", 10)
          .style("fill", "rgba(255, 255, 255, 0.95)")
          .style("stroke", "#52AAA3")
          .style("stroke-width", "1px")

        marker
          .append("text")
          .attr("x", x)
          .attr("y", y - 22)
          .attr("text-anchor", "middle")
          .style("fill", "#1e293b")
          .style("font-size", "10px")
          .style("font-weight", "600")
          .text(partner.name.length > 15 ? partner.name.substring(0, 15) + "..." : partner.name)
      }
    })
  }, [
    svg,
    currentZoom,
    filteredPartners,
    hoveredPartner,
    selectedPartner,
    animationSpeed,
    projection,
    onPartnerClick,
    onPartnerHover,
  ])
}
