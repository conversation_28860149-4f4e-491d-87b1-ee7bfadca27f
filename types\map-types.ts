import type React from "react"
export interface CountryFeature {
  type: "Feature"
  properties: {
    NAME: string
    ISO_A3: string
  }
  geometry: any
}

export interface WorldData {
  countries: {
    type: "FeatureCollection"
    features: CountryFeature[]
  }
}

export interface ServiceType {
  id: string
  name: string
  color: string
  icon: React.ComponentType<{ className?: string }>
}

export interface Region {
  id: string
  name: string
  center: [number, number]
  zoom: number
  bounds: [[number, number], [number, number]]
  services: string[]
}

export interface Partner {
  id: string
  name: string
  country: string
  coordinates: [number, number]
  services: string[]
  coverage: string[]
  streamlnkIntegrated: boolean
  tier: "premium" | "standard" | "basic"
  contact: {
    phone: string
    email: string
    website: string
  }
  description: string
  establishedYear: number
  employeeCount: string
}

export interface Route {
  id: string
  from: string
  to: string
  services: string[]
  intensity: "high" | "medium" | "low"
  status: "active" | "maintenance" | "planned"
  frequency: string
  transitTime: string
  capacity: string
  description: string
}

export interface LiveAsset {
  id: string
  type: "ship" | "truck" | "train" | "plane"
  routeId: string
  progress: number
  speed: number
  cargo: string
  value: number
  status: "on-time" | "delayed" | "critical"
  eta: string
  currentLocation: [number, number]
  destination: string
  origin: string
}

export interface RegionalStats {
  regionId: string
  activeShipments: number
  totalValue: number
  averageDelay: number
  throughput: number
  alerts: number
  efficiency: number
}

export interface LiveEvent {
  id: string
  type: "delay" | "arrival" | "departure" | "alert" | "milestone"
  severity: "low" | "medium" | "high" | "critical"
  message: string
  location: [number, number]
  timestamp: Date
  assetId?: string
  routeId?: string
  autoHide: boolean
}

export interface RealTimeData {
  assets: LiveAsset[]
  regionalStats: RegionalStats[]
  events: LiveEvent[]
  globalMetrics: {
    totalShipments: number
    totalValue: number
    averageDelay: number
    systemHealth: number
  }
  lastUpdate: Date
}
