# Color Updates Summary

## Brand Colors Applied
Based on your brand color palette:
- **Primary**: `#004235` (dark teal)
- **Secondary**: `#028475` (lighter teal) 
- **Link**: `#028475` (same as secondary)

## Color Mapping Changes

### 1. Map Canvas (`components/map/map-canvas.tsx`)
**Route Intensity Colors:**
- High intensity: `#eab308` → `#004235` (primary)
- Medium intensity: `#3b82f6` → `#028475` (secondary)
- Low intensity: `#eab308` → `#52AAA3` (existing teal)

**Asset Status Colors:**
- On-time: `#10b981` → `#028475` (secondary)
- Delayed: `#f59e0b` → `#004235` (primary)
- Critical: `#ef4444` (kept red for urgency)

### 2. Assets Layer (`components/map/layers/assets-layer.tsx`)
**Asset Status Colors:**
- On-time: `#10b981` → `#028475` (secondary)
- Delayed: `#f59e0b` → `#004235` (primary)
- Critical: `#ef4444` (kept red for urgency)

### 3. Events Layer (`components/map/layers/events-layer.tsx`)
**Event Severity Colors:**
- High severity: `#f59e0b` → `#004235` (primary)
- Medium severity: `#3b82f6` → `#028475` (secondary)
- Low severity: `#10b981` → `#52AAA3` (existing teal)
- Critical: `#ef4444` (kept red for urgency)

### 4. Partners Layer (`components/map/layers/partners-layer.tsx`)
**Partner Integration Status:**
- Integrated: `#52AAA3` → `#028475` (secondary)
- Not integrated: `#eab308` → `#004235` (primary)

**Tier Indicators:**
- Premium: `#10b981` → `#028475` (secondary)
- Standard: `#3b82f6` → `#004235` (primary)
- Basic: `#6b7280` (kept gray)

### 5. Panel Components

#### Asset Panel (`components/map/asset-panel.tsx`)
**Status Badge Colors:**
- On-time: `bg-green-100 text-green-800` → `bg-teal-100 text-teal-800`
- Delayed: `bg-yellow-100 text-yellow-800` → `bg-slate-100 text-slate-800`
- Critical: `bg-red-100 text-red-800` (kept red)

#### Route Panel (`components/map/route-panel.tsx`)
**Status Badge Colors:**
- Active: `bg-green-100 text-green-800` → `bg-teal-100 text-teal-800`
- Maintenance: `bg-orange-100 text-orange-800` → `bg-slate-100 text-slate-800`
- Planned: `bg-blue-100 text-blue-800` (kept blue)

**Intensity Badge Colors:**
- High: `bg-red-100 text-red-800` → `bg-slate-100 text-slate-800`
- Medium: `bg-yellow-100 text-yellow-800` → `bg-teal-100 text-teal-800`
- Low: `bg-green-100 text-green-800` → `bg-emerald-100 text-emerald-800`

**Asset Status Dots:**
- On-time: `bg-green-500` → `bg-teal-500`
- Delayed: `bg-yellow-500` → `bg-slate-500`
- Critical: `bg-red-500` (kept red)

#### Partner Panel (`components/map/partner-panel.tsx`)
**Tier Badge Colors:**
- Premium: `bg-green-100 text-green-800` → `bg-teal-100 text-teal-800`
- Standard: `bg-blue-100 text-blue-800` → `bg-slate-100 text-slate-800`
- Basic: `bg-gray-100 text-gray-800` (kept gray)

#### Status Panel (`components/map/status-panel.tsx`)
**Live Assets Indicator:**
- Assets dot: `bg-yellow-600` → `bg-teal-600`

### 6. Service Types (`data/map-data.ts`)
**Service Color Updates:**
- Land Freight: `#3b82f6` → `#028475` (secondary)
- Sea Freight: `#0f766e` → `#004235` (primary)
- Customs Clearance: `#eab308` → `#52AAA3` (existing teal)
- Rail Freight: `#dc2626` (kept red)
- Warehouse & Packing: `#7c3aed` (kept purple)

## Color Strategy

### Primary Brand Color (`#004235`)
Used for:
- High-priority/intensity items
- Non-integrated partners
- Delayed assets
- High-severity events
- Sea freight services

### Secondary Brand Color (`#028475`)
Used for:
- Medium-priority items
- Integrated partners
- On-time assets
- Premium tiers
- Land freight services

### Supporting Colors
- **Red (`#ef4444`)**: Reserved for critical/urgent items
- **Teal (`#52AAA3`)**: Used for low-priority items and customs
- **Gray/Slate**: Used for neutral states
- **Blue**: Kept for planned/informational states

## Benefits

1. **Brand Consistency**: All map elements now reflect your brand identity
2. **Visual Hierarchy**: Primary colors draw attention to important elements
3. **Accessibility**: Maintained contrast ratios for readability
4. **Semantic Meaning**: Colors now align with brand values while preserving urgency indicators

The color updates maintain the functional meaning of different states while aligning the visual design with your brand palette.
