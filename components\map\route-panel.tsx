"use client"

import { Zap, X, TrendingUp, Activity, Clock } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { serviceTypes } from "@/data/map-data"
import type { Route, LiveAsset } from "@/types/map-types"

interface RoutePanelProps {
  selectedRoute: Route
  filteredAssets: LiveAsset[]
  onClose: () => void
  onSelectAsset: (asset: LiveAsset) => void
}

export function RoutePanel({ selectedRoute, filteredAssets, onClose, onSelectAsset }: RoutePanelProps) {
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-300"
      case "maintenance":
        return "bg-orange-100 text-orange-800 border-orange-300"
      case "planned":
        return "bg-blue-100 text-blue-800 border-blue-300"
      default:
        return "bg-gray-100 text-gray-800 border-gray-300"
    }
  }

  const getIntensityBadgeColor = (intensity: string) => {
    switch (intensity) {
      case "high":
        return "bg-red-100 text-red-800 border-red-300"
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-300"
      case "low":
        return "bg-green-100 text-green-800 border-green-300"
      default:
        return "bg-gray-100 text-gray-800 border-gray-300"
    }
  }

  const getIntensityIcon = (intensity: string) => {
    switch (intensity) {
      case "high":
        return <TrendingUp className="w-3 h-3" />
      case "medium":
        return <Activity className="w-3 h-3" />
      case "low":
        return <Clock className="w-3 h-3" />
      default:
        return <Activity className="w-3 h-3" />
    }
  }

  const routeAssets = filteredAssets.filter((asset) => asset.routeId === selectedRoute.id)

  return (
    <div className="fixed inset-y-0 left-0 w-96 z-30 transform transition-transform duration-300 ease-in-out">
      <Card className="h-full glassmorphism border-r border-slate-200 rounded-none overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center gap-3">
              <Zap className="w-8 h-8 text-[#52AAA3]" />
              <div>
                <h2 className="text-xl font-bold text-slate-900">Route Information</h2>
                <p className="text-slate-600 text-sm">
                  {selectedRoute.from} → {selectedRoute.to}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-slate-600 hover:text-slate-900 hover:bg-slate-100"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Status Badges */}
          <div className="flex gap-2 mb-6">
            <Badge className={getStatusBadgeColor(selectedRoute.status)}>{selectedRoute.status.toUpperCase()}</Badge>
            <Badge className={getIntensityBadgeColor(selectedRoute.intensity)}>
              {getIntensityIcon(selectedRoute.intensity)}
              {selectedRoute.intensity.toUpperCase()} INTENSITY
            </Badge>
          </div>

          {/* Description */}
          <div className="mb-6">
            <h3 className="text-slate-900 font-semibold mb-2">Route Description</h3>
            <p className="text-slate-600 text-sm leading-relaxed">{selectedRoute.description}</p>
          </div>

          <Separator className="bg-slate-200 mb-6" />

          {/* Services */}
          <div className="mb-6">
            <h3 className="text-slate-900 font-semibold mb-3">Services Available</h3>
            <div className="grid grid-cols-2 gap-2">
              {selectedRoute.services.map((serviceId) => {
                const service = serviceTypes.find((s) => s.id === serviceId)
                if (!service) return null
                const Icon = service.icon
                return (
                  <div
                    key={serviceId}
                    className="flex items-center gap-2 p-2 bg-slate-50 rounded-lg border border-slate-200"
                  >
                    <div className="w-3 h-3 rounded-full" style={{ backgroundColor: service.color }} />
                    <Icon className="w-4 h-4 text-slate-600" />
                    <span className="text-slate-900 text-xs">{service.name}</span>
                  </div>
                )
              })}
            </div>
          </div>

          <Separator className="bg-slate-200 mb-6" />

          {/* Route Metrics */}
          <div className="mb-6">
            <h3 className="text-slate-900 font-semibold mb-3">Route Metrics</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-slate-600 text-sm">Frequency</span>
                <span className="text-slate-900 font-medium">{selectedRoute.frequency}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-slate-600 text-sm">Transit Time</span>
                <span className="text-slate-900 font-medium">{selectedRoute.transitTime}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-slate-600 text-sm">Capacity</span>
                <span className="text-slate-900 font-medium">{selectedRoute.capacity}</span>
              </div>
            </div>
          </div>

          {/* Live Assets on Route */}
          <div className="mb-6">
            <h3 className="text-slate-900 font-semibold mb-3">Live Assets on Route</h3>
            <div className="space-y-2">
              {routeAssets.map((asset) => (
                <div
                  key={asset.id}
                  className="flex items-center gap-3 p-2 bg-slate-50 rounded-lg cursor-pointer hover:bg-slate-100 border border-slate-200"
                  onClick={() => onSelectAsset(asset)}
                >
                  <div
                    className={`w-3 h-3 rounded-full ${
                      asset.status === "on-time"
                        ? "bg-green-500"
                        : asset.status === "delayed"
                          ? "bg-yellow-500"
                          : "bg-red-500"
                    }`}
                  />
                  <div className="flex-1">
                    <p className="text-slate-900 text-sm font-medium">{asset.cargo}</p>
                    <p className="text-slate-600 text-xs">{Math.round(asset.progress * 100)}% complete</p>
                  </div>
                  <span className="text-[#52AAA3] text-xs">{asset.type}</span>
                </div>
              ))}
              {routeAssets.length === 0 && <p className="text-slate-600 text-sm">No active assets on this route</p>}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button className="flex-1 bg-[#52AAA3] hover:bg-[#52AAA3]/90 text-white">Book Shipment</Button>
            <Button variant="outline" className="border-[#52AAA3] text-[#52AAA3] hover:bg-[#52AAA3]/10">
              Get Quote
            </Button>
          </div>
        </div>
      </Card>
    </div>
  )
}
