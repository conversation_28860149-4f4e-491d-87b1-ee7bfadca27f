"use client"

import { useEffect, useRef, useState, useCallback, useMemo } from "react"
import * as d3 from "d3"
import { feature } from "topojson-client"
import { TransformWrapper, TransformComponent } from "react-zoom-pan-pinch"
import { Globe } from "lucide-react"

// Import components
import { MapHeader } from "@/components/map/map-header"
import { AssetPanel } from "@/components/map/asset-panel"
import { RoutePanel } from "@/components/map/route-panel"
import { PartnerPanel } from "@/components/map/partner-panel"
import { RealTimeControls } from "@/components/map/real-time-controls"
import { LiveEventsPanel } from "@/components/map/live-events-panel"
import { RegionalNavigation } from "@/components/map/regional-navigation"
import { MapControls } from "@/components/map/map-controls"
import { StatusPanel } from "@/components/map/status-panel"
import { Button } from "@/components/ui/button"

// Import data and types
import { serviceTypes, regions, partners, routes } from "@/data/map-data"
import type {
  WorldData,
  Partner,
  Route,
  LiveAsset,
  RegionalStats,
  LiveEvent,
  RealTimeData,
  Region,
} from "@/types/map-types"

export default function InteractiveGlobalMap() {
  const svgRef = useRef<SVGSVGElement>(null)
  const transformRef = useRef<any>(null)
  const animationFrameRef = useRef<number>()
  const lastUpdateRef = useRef<number>(0)
  const assetsRef = useRef<LiveAsset[]>([])

  // Core state
  const [worldData, setWorldData] = useState<WorldData | null>(null)
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null)
  const [selectedRegion, setSelectedRegion] = useState<string | null>(null)
  const [selectedPartner, setSelectedPartner] = useState<Partner | null>(null)
  const [selectedRoute, setSelectedRoute] = useState<Route | null>(null)
  const [selectedAsset, setSelectedAsset] = useState<LiveAsset | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [currentZoom, setCurrentZoom] = useState(1)
  const [hoveredPartner, setHoveredPartner] = useState<string | null>(null)
  const [hoveredRoute, setHoveredRoute] = useState<string | null>(null)

  // Filter state
  const [activeServices, setActiveServices] = useState<Set<string>>(new Set(serviceTypes.map((s) => s.id)))
  const [showRoutes, setShowRoutes] = useState(true)
  const [routeIntensityFilter, setRouteIntensityFilter] = useState<string[]>(["high", "medium", "low"])
  const [animationSpeed, setAnimationSpeed] = useState([1])

  // Real-time data state
  const [realTimeData, setRealTimeData] = useState<RealTimeData>({
    assets: [],
    regionalStats: [],
    events: [],
    globalMetrics: {
      totalShipments: 0,
      totalValue: 0,
      averageDelay: 0,
      systemHealth: 100,
    },
    lastUpdate: new Date(),
  })

  // Real-time controls
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true)
  const [showAssets, setShowAssets] = useState(true)
  const [showHeatmap, setShowHeatmap] = useState(true)
  const [showEvents, setShowEvents] = useState(true)
  const [showRegionalStats, setShowRegionalStats] = useState(true)
  const [updateInterval, setUpdateInterval] = useState([2000])

  // Sidebar minimization state
  const [isRealTimeControlsMinimized, setIsRealTimeControlsMinimized] = useState(false)
  const [isRegionalNavMinimized, setIsRegionalNavMinimized] = useState(false)
  const [isLiveEventsMinimized, setIsLiveEventsMinimized] = useState(false)
  const [isStatusPanelMinimized, setIsStatusPanelMinimized] = useState(false)

  // Calculate if left sidebar should be shown
  const isLeftSidebarVisible = !isRegionalNavMinimized || !isLiveEventsMinimized || !isStatusPanelMinimized

  // Map dimensions
  const width = 1200
  const height = 600

  // Stable projection
  const projection = useMemo(
    () =>
      d3
        .geoMercator()
        .scale(150)
        .translate([width / 2, height / 2]),
    [width, height],
  )

  const pathGenerator = useMemo(() => d3.geoPath().projection(projection), [projection])

  // Stable arc generator
  const createArc = useCallback(
    (from: [number, number], to: [number, number]) => {
      const geoInterpolate = d3.geoInterpolate(from, to)
      const points = []
      for (let i = 0; i <= 100; i++) {
        points.push(geoInterpolate(i / 100))
      }
      return points.map((point) => projection(point)).filter((p) => p !== null) as [number, number][]
    },
    [projection],
  )

  // Generate mock real-time data - stable function
  const generateMockData = useCallback((): RealTimeData => {
    const assets: LiveAsset[] = []
    const regionalStats: RegionalStats[] = []
    const events: LiveEvent[] = []

    // Generate assets for active routes
    routes
      .filter((route) => route.status === "active")
      .forEach((route) => {
        const assetCount = route.intensity === "high" ? 3 : route.intensity === "medium" ? 2 : 1

        for (let i = 0; i < assetCount; i++) {
          const fromPartner = partners.find((p) => p.id === route.from)
          const toPartner = partners.find((p) => p.id === route.to)

          if (fromPartner && toPartner) {
            const assetType = route.services.includes("sea")
              ? "ship"
              : route.services.includes("rail")
                ? "train"
                : route.services.includes("land")
                  ? "truck"
                  : "ship"

            const progress = Math.random()
            const statuses = ["on-time", "delayed", "critical"] as const
            const status = statuses[Math.floor(Math.random() * statuses.length)]

            // Simple interpolation for current location
            const lat = fromPartner.coordinates[1] + (toPartner.coordinates[1] - fromPartner.coordinates[1]) * progress
            const lng = fromPartner.coordinates[0] + (toPartner.coordinates[0] - fromPartner.coordinates[0]) * progress

            assets.push({
              id: `${route.id}-asset-${i}`,
              type: assetType,
              routeId: route.id,
              progress,
              speed: 0.001 + Math.random() * 0.002,
              cargo: `Container ${Math.floor(Math.random() * 10000)}`,
              value: Math.floor(Math.random() * 1000000) + 100000,
              status,
              eta: new Date(Date.now() + Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
              currentLocation: [lng, lat],
              destination: toPartner.name,
              origin: fromPartner.name,
            })
          }
        }
      })

    // Generate regional stats
    regions.forEach((region) => {
      regionalStats.push({
        regionId: region.id,
        activeShipments: Math.floor(Math.random() * 100) + 20,
        totalValue: Math.floor(Math.random() * 10000000) + 1000000,
        averageDelay: Math.random() * 24,
        throughput: Math.floor(Math.random() * 1000) + 100,
        alerts: Math.floor(Math.random() * 5),
        efficiency: Math.floor(Math.random() * 20) + 80,
      })
    })

    // Generate random events
    const eventTypes = ["delay", "arrival", "departure", "alert", "milestone"] as const
    const severities = ["low", "medium", "high", "critical"] as const

    for (let i = 0; i < 5; i++) {
      const randomPartner = partners[Math.floor(Math.random() * partners.length)]
      const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)]
      const severity = severities[Math.floor(Math.random() * severities.length)]

      events.push({
        id: `event-${Date.now()}-${i}`,
        type: eventType,
        severity,
        message: `${eventType.charAt(0).toUpperCase() + eventType.slice(1)} at ${randomPartner.name}`,
        location: randomPartner.coordinates,
        timestamp: new Date(),
        autoHide: severity === "low",
      })
    }

    return {
      assets,
      regionalStats,
      events,
      globalMetrics: {
        totalShipments: assets.length,
        totalValue: assets.reduce((sum, asset) => sum + asset.value, 0),
        averageDelay: regionalStats.reduce((sum, stat) => sum + stat.averageDelay, 0) / regionalStats.length,
        systemHealth: Math.floor(Math.random() * 10) + 90,
      },
      lastUpdate: new Date(),
    }
  }, [])

  // Real-time data simulation
  useEffect(() => {
    if (!isRealTimeEnabled) return

    const interval = setInterval(() => {
      const newData = generateMockData()
      setRealTimeData(newData)
      assetsRef.current = newData.assets
    }, updateInterval[0])

    return () => clearInterval(interval)
  }, [isRealTimeEnabled, updateInterval, generateMockData])

  // Animation loop for moving assets - using refs to avoid state updates
  useEffect(() => {
    if (!showAssets || !isRealTimeEnabled) return

    const animate = (timestamp: number) => {
      if (timestamp - lastUpdateRef.current > 50) {
        // Update at 20 FPS
        assetsRef.current = assetsRef.current.map((asset) => ({
          ...asset,
          progress: Math.min(1, asset.progress + asset.speed * animationSpeed[0]),
        }))

        // Force re-render by updating the real-time data
        setRealTimeData((prevData) => ({
          ...prevData,
          assets: [...assetsRef.current],
        }))

        lastUpdateRef.current = timestamp
      }
      animationFrameRef.current = requestAnimationFrame(animate)
    }

    animationFrameRef.current = requestAnimationFrame(animate)

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [showAssets, isRealTimeEnabled, animationSpeed])

  // Load world data
  useEffect(() => {
    const loadWorldData = async () => {
      try {
        setIsLoading(true)

        const dataSources = [
          "https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson",
          "https://cdn.jsdelivr.net/npm/world-atlas@1/world/110m.json",
          "https://unpkg.com/world-atlas@1/world/110m.json",
        ]

        let worldGeoData = null

        for (const source of dataSources) {
          try {
            const response = await fetch(source)
            if (!response.ok) continue

            const contentType = response.headers.get("content-type")

            if (source.includes("geojson") || contentType?.includes("application/json")) {
              const geoJsonData = await response.json()
              worldGeoData = { countries: geoJsonData }
              break
            } else {
              const topology = await response.json()
              if (topology.objects && topology.objects.countries) {
                const countries = feature(topology, topology.objects.countries) as any
                worldGeoData = { countries }
                break
              } else if (topology.objects && topology.objects.land) {
                const countries = feature(topology, topology.objects.land) as any
                worldGeoData = { countries }
                break
              }
            }
          } catch (sourceError) {
            continue
          }
        }

        if (!worldGeoData) {
          worldGeoData = {
            countries: {
              type: "FeatureCollection",
              features: [
                {
                  type: "Feature",
                  properties: { NAME: "World", ISO_A3: "WLD" },
                  geometry: {
                    type: "Polygon",
                    coordinates: [
                      [
                        [-180, -90],
                        [180, -90],
                        [180, 90],
                        [-180, 90],
                        [-180, -90],
                      ],
                    ],
                  },
                },
              ],
            },
          }
        }

        setWorldData(worldGeoData)
      } catch (error) {
        console.error("Error loading world data:", error)
        const fallbackData = {
          countries: {
            type: "FeatureCollection",
            features: [
              {
                type: "Feature",
                properties: { NAME: "World", ISO_A3: "WLD" },
                geometry: {
                  type: "Polygon",
                  coordinates: [
                    [
                      [-180, -90],
                      [180, -90],
                      [180, 90],
                      [-180, 90],
                      [-180, -90],
                    ],
                  ],
                },
              },
            ],
          },
        }
        setWorldData(fallbackData)
      } finally {
        setIsLoading(false)
      }
    }

    loadWorldData()
  }, [])

  // Initialize real-time data
  useEffect(() => {
    const initialData = generateMockData()
    setRealTimeData(initialData)
    assetsRef.current = initialData.assets
  }, [generateMockData])

  // Filtered data
  const filteredPartners = useMemo(
    () => partners.filter((partner) => partner.services.some((service) => activeServices.has(service))),
    [activeServices],
  )

  const filteredRoutes = useMemo(
    () =>
      routes.filter(
        (route) =>
          showRoutes &&
          route.services.some((service) => activeServices.has(service)) &&
          routeIntensityFilter.includes(route.intensity),
      ),
    [showRoutes, activeServices, routeIntensityFilter],
  )

  const filteredAssets = useMemo(
    () =>
      realTimeData.assets.filter((asset) => {
        const route = routes.find((r) => r.id === asset.routeId)
        return route && route.services.some((service) => activeServices.has(service))
      }),
    [realTimeData.assets, activeServices],
  )

  // Main rendering effect - optimized dependencies
  useEffect(() => {
    if (!worldData || !svgRef.current) return

    const svg = d3.select(svgRef.current)
    svg.selectAll("*").remove()

    // Create defs for patterns and gradients
    const defs = svg.append("defs")

    // Marching ants pattern
    const marchingAnts = defs
      .append("pattern")
      .attr("id", "marching-ants")
      .attr("patternUnits", "userSpaceOnUse")
      .attr("width", 20)
      .attr("height", 4)

    marchingAnts.append("rect").attr("width", 20).attr("height", 4).attr("fill", "transparent")
    marchingAnts.append("rect").attr("x", 0).attr("y", 0).attr("width", 10).attr("height", 4).attr("fill", "#52AAA3")

    marchingAnts
      .append("animateTransform")
      .attr("attributeName", "patternTransform")
      .attr("type", "translate")
      .attr("values", "0,0;20,0;0,0")
      .attr("dur", `${2 / animationSpeed[0]}s`)
      .attr("repeatCount", "indefinite")

    // Pulsing gradient
    const pulsingGradient = defs
      .append("linearGradient")
      .attr("id", "pulsing-gradient")
      .attr("x1", "0%")
      .attr("y1", "0%")
      .attr("x2", "100%")
      .attr("y2", "0%")

    pulsingGradient.append("stop").attr("offset", "0%").attr("stop-color", "#52AAA3").attr("stop-opacity", 0.3)
    pulsingGradient.append("stop").attr("offset", "50%").attr("stop-color", "#52AAA3").attr("stop-opacity", 1)
    pulsingGradient.append("stop").attr("offset", "100%").attr("stop-color", "#52AAA3").attr("stop-opacity", 0.3)

    pulsingGradient
      .selectAll("stop")
      .append("animate")
      .attr("attributeName", "stop-opacity")
      .attr("values", "0.3;1;0.3")
      .attr("dur", `${1.5 / animationSpeed[0]}s`)
      .attr("repeatCount", "indefinite")

    // Asset trail gradient
    const trailGradient = defs
      .append("linearGradient")
      .attr("id", "asset-trail")
      .attr("x1", "0%")
      .attr("y1", "0%")
      .attr("x2", "100%")
      .attr("y2", "0%")

    trailGradient.append("stop").attr("offset", "0%").attr("stop-color", "#52AAA3").attr("stop-opacity", 0)
    trailGradient.append("stop").attr("offset", "70%").attr("stop-color", "#52AAA3").attr("stop-opacity", 0.3)
    trailGradient.append("stop").attr("offset", "100%").attr("stop-color", "#52AAA3").attr("stop-opacity", 0.8)

    // Create country paths
    const countries = svg
      .selectAll("path")
      .data(worldData.countries.features || [])
      .enter()
      .append("path")
      .attr("d", pathGenerator as any)
      .attr("class", "country")
      .style("fill", "#5DC1B9")
      .style("stroke", "#52AAA3")
      .style("stroke-width", "0.5px")
      .style("cursor", "pointer")
      .style("transition", "all 0.3s ease")

    // Regional heatmap overlay
    if (showHeatmap && currentZoom < 3) {
      const heatmapGroup = svg.append("g").attr("class", "heatmap")

      regions.forEach((region) => {
        const stats = realTimeData.regionalStats.find((s) => s.regionId === region.id)
        if (!stats) return

        const [x, y] = projection(region.center) || [0, 0]
        const intensity = Math.min(stats.activeShipments / 100, 1)
        const radius = 50 + intensity * 100

        heatmapGroup
          .append("circle")
          .attr("cx", x)
          .attr("cy", y)
          .attr("r", radius)
          .style("fill", `rgba(82, 170, 163, ${intensity * 0.3})`)
          .style("stroke", "#52AAA3")
          .style("stroke-width", "1px")
          .style("stroke-opacity", intensity * 0.5)
          .style("pointer-events", "none")

        if (intensity > 0.7) {
          heatmapGroup
            .append("circle")
            .attr("cx", x)
            .attr("cy", y)
            .attr("r", radius * 0.8)
            .style("fill", "none")
            .style("stroke", "#52AAA3")
            .style("stroke-width", "2px")
            .style("opacity", 0.6)
            .style("pointer-events", "none")
            .append("animate")
            .attr("attributeName", "r")
            .attr("values", `${radius * 0.8};${radius * 1.2};${radius * 0.8}`)
            .attr("dur", "3s")
            .attr("repeatCount", "indefinite")
        }
      })
    }

    // Add connection lines (routes)
    if (showRoutes && currentZoom >= 0.8) {
      const routeGroup = svg.append("g").attr("class", "routes")

      filteredRoutes.forEach((route) => {
        const fromPartner = partners.find((p) => p.id === route.from)
        const toPartner = partners.find((p) => p.id === route.to)

        if (!fromPartner || !toPartner) return

        const arcPoints = createArc(fromPartner.coordinates, toPartner.coordinates)
        if (arcPoints.length === 0) return

        const lineGenerator = d3
          .line()
          .x((d) => d[0])
          .y((d) => d[1])
          .curve(d3.curveCardinal)

        const pathData = lineGenerator(arcPoints)
        if (!pathData) return

        const getRouteStyle = (route: Route) => {
          switch (route.intensity) {
            case "high":
              return { width: 4, opacity: 0.8, color: "#52AAA3" }
            case "medium":
              return { width: 3, opacity: 0.6, color: "#3b82f6" }
            case "low":
              return { width: 2, opacity: 0.4, color: "#eab308" }
            default:
              return { width: 2, opacity: 0.4, color: "#6b7280" }
          }
        }

        const style = getRouteStyle(route)

        // Background line
        routeGroup
          .append("path")
          .attr("d", pathData)
          .attr("class", `route-bg route-${route.id}`)
          .style("fill", "none")
          .style("stroke", "#000")
          .style("stroke-width", style.width + 2)
          .style("opacity", 0.3)
          .style("pointer-events", "none")

        // Main route line
        const routeLine = routeGroup
          .append("path")
          .attr("d", pathData)
          .attr("class", `route route-${route.id}`)
          .style("fill", "none")
          .style("stroke", style.color)
          .style("stroke-width", style.width)
          .style("opacity", hoveredRoute === route.id ? 1 : style.opacity)
          .style("cursor", "pointer")
          .style("transition", "all 0.3s ease")

        if (route.status === "planned") {
          routeLine.style("stroke-dasharray", "10,5")
        }

        routeLine
          .on("mouseenter", function () {
            setHoveredRoute(route.id)
            d3.select(this)
              .style("opacity", 1)
              .style("stroke-width", style.width + 2)
          })
          .on("mouseleave", function () {
            if (selectedRoute?.id !== route.id) {
              setHoveredRoute(null)
              d3.select(this).style("opacity", style.opacity).style("stroke-width", style.width)
            }
          })
          .on("click", () => {
            setSelectedRoute(route)
          })
      })
    }

    // Add live assets
    if (showAssets && currentZoom >= 1.2) {
      const assetGroup = svg.append("g").attr("class", "assets")

      filteredAssets.forEach((asset) => {
        const route = routes.find((r) => r.id === asset.routeId)
        if (!route) return

        const fromPartner = partners.find((p) => p.id === route.from)
        const toPartner = partners.find((p) => p.id === route.to)
        if (!fromPartner || !toPartner) return

        const arcPoints = createArc(fromPartner.coordinates, toPartner.coordinates)
        if (arcPoints.length === 0) return

        const currentIndex = Math.floor(asset.progress * (arcPoints.length - 1))
        const currentPoint = arcPoints[currentIndex]
        if (!currentPoint) return

        const [x, y] = currentPoint

        // Asset trail
        if (asset.progress > 0.1) {
          const trailStart = Math.max(0, currentIndex - 10)
          const trailPoints = arcPoints.slice(trailStart, currentIndex + 1)

          if (trailPoints.length > 1) {
            const trailGenerator = d3
              .line()
              .x((d) => d[0])
              .y((d) => d[1])
              .curve(d3.curveCardinal)

            const trailPath = trailGenerator(trailPoints)
            if (trailPath) {
              assetGroup
                .append("path")
                .attr("d", trailPath)
                .attr("class", `asset-trail asset-${asset.id}`)
                .style("fill", "none")
                .style("stroke", "url(#asset-trail)")
                .style("stroke-width", 3)
                .style("pointer-events", "none")
            }
          }
        }

        // Asset marker
        const assetMarker = assetGroup
          .append("g")
          .attr("class", `asset-marker asset-${asset.id}`)
          .attr("transform", `translate(${x}, ${y})`)
          .style("cursor", "pointer")
          .on("click", () => setSelectedAsset(asset))

        const getAssetColor = (status: string) => {
          switch (status) {
            case "on-time":
              return "#10b981"
            case "delayed":
              return "#f59e0b"
            case "critical":
              return "#ef4444"
            default:
              return "#52AAA3"
          }
        }

        const assetColor = getAssetColor(asset.status)

        // Pulsing background for critical assets
        if (asset.status === "critical") {
          assetMarker
            .append("circle")
            .attr("r", 12)
            .style("fill", assetColor)
            .style("opacity", 0.3)
            .append("animate")
            .attr("attributeName", "r")
            .attr("values", "12;18;12")
            .attr("dur", "2s")
            .attr("repeatCount", "indefinite")
        }

        // Main asset circle
        assetMarker
          .append("circle")
          .attr("r", 6)
          .style("fill", assetColor)
          .style("stroke", "#fff")
          .style("stroke-width", "2px")

        // Asset type icon
        const getAssetIcon = (type: string) => {
          switch (type) {
            case "ship":
              return "🚢"
            case "truck":
              return "🚛"
            case "train":
              return "🚂"
            case "plane":
              return "✈️"
            default:
              return "📦"
          }
        }

        assetMarker
          .append("text")
          .attr("text-anchor", "middle")
          .attr("dy", "0.3em")
          .style("font-size", "8px")
          .style("pointer-events", "none")
          .text(getAssetIcon(asset.type))

        // Asset label (show when selected)
        if (selectedAsset?.id === asset.id) {
          assetMarker
            .append("rect")
            .attr("x", -30)
            .attr("y", -25)
            .attr("width", 60)
            .attr("height", 15)
            .attr("rx", 7)
            .style("fill", "rgba(255, 255, 255, 0.9)")
            .style("stroke", assetColor)
            .style("stroke-width", "1px")

          assetMarker
            .append("text")
            .attr("text-anchor", "middle")
            .attr("y", -17)
            .style("fill", "#1e293b")
            .style("font-size", "8px")
            .style("font-weight", "600")
            .style("pointer-events", "none")
            .text(asset.cargo)
        }
      })
    }

    // Add live events
    if (showEvents) {
      const eventGroup = svg.append("g").attr("class", "events")

      realTimeData.events.forEach((event) => {
        const [x, y] = projection(event.location) || [0, 0]

        const getEventColor = (severity: string) => {
          switch (severity) {
            case "critical":
              return "#ef4444"
            case "high":
              return "#f59e0b"
            case "medium":
              return "#3b82f6"
            case "low":
              return "#10b981"
            default:
              return "#52AAA3"
          }
        }

        const eventColor = getEventColor(event.severity)

        const eventMarker = eventGroup
          .append("g")
          .attr("class", `event-marker event-${event.id}`)
          .attr("transform", `translate(${x}, ${y})`)
          .style("cursor", "pointer")

        // Pulsing circle for alerts
        if (event.severity === "critical" || event.severity === "high") {
          eventMarker
            .append("circle")
            .attr("r", 8)
            .style("fill", "none")
            .style("stroke", eventColor)
            .style("stroke-width", "2px")
            .style("opacity", 0.8)
            .append("animate")
            .attr("attributeName", "r")
            .attr("values", "8;15;8")
            .attr("dur", "2s")
            .attr("repeatCount", "indefinite")
        }

        // Main event circle
        eventMarker
          .append("circle")
          .attr("r", 4)
          .style("fill", eventColor)
          .style("stroke", "#fff")
          .style("stroke-width", "1px")

        // Auto-hide low severity events
        if (event.autoHide) {
          eventMarker.transition().delay(5000).duration(1000).style("opacity", 0).remove()
        }
      })
    }

    // Add regional statistics overlays
    if (showRegionalStats && currentZoom < 2.5) {
      const statsGroup = svg.append("g").attr("class", "regional-stats")

      regions.forEach((region) => {
        const stats = realTimeData.regionalStats.find((s) => s.regionId === region.id)
        if (!stats) return

        const [x, y] = projection(region.center) || [0, 0]

        const statsBox = statsGroup
          .append("g")
          .attr("class", `stats-box stats-${region.id}`)
          .attr("transform", `translate(${x}, ${y + 40})`)
          .style("cursor", "pointer")

        statsBox
          .append("rect")
          .attr("x", -50)
          .attr("y", -20)
          .attr("width", 100)
          .attr("height", 40)
          .attr("rx", 8)
          .style("fill", "rgba(255, 255, 255, 0.9)")
          .style("stroke", "#52AAA3")
          .style("stroke-width", "1px")

        statsBox
          .append("text")
          .attr("text-anchor", "middle")
          .attr("y", -8)
          .style("fill", "#52AAA3")
          .style("font-size", "10px")
          .style("font-weight", "600")
          .text(`${stats.activeShipments} Active`)

        statsBox
          .append("text")
          .attr("text-anchor", "middle")
          .attr("y", 5)
          .style("fill", "#475569")
          .style("font-size", "8px")
          .text(`$${(stats.totalValue / 1000000).toFixed(1)}M`)

        statsBox
          .append("text")
          .attr("text-anchor", "middle")
          .attr("y", 15)
          .style("fill", stats.efficiency > 90 ? "#10b981" : stats.efficiency > 80 ? "#f59e0b" : "#ef4444")
          .style("font-size", "8px")
          .text(`${stats.efficiency}% Eff.`)
      })
    }

    // Add partner markers
    if (currentZoom >= 1.5) {
      const partnerGroup = svg.append("g").attr("class", "partners")

      filteredPartners.forEach((partner) => {
        const [x, y] = projection(partner.coordinates) || [0, 0]

        const marker = partnerGroup
          .append("g")
          .attr("class", `partner-marker partner-${partner.id}`)
          .style("cursor", "pointer")
          .on("click", () => setSelectedPartner(partner))
          .on("mouseenter", () => setHoveredPartner(partner.id))
          .on("mouseleave", () => setHoveredPartner(null))

        // Pulsing background
        if (hoveredPartner === partner.id || selectedPartner?.id === partner.id) {
          marker
            .append("circle")
            .attr("cx", x)
            .attr("cy", y)
            .attr("r", 20)
            .style("fill", partner.streamlnkIntegrated ? "#52AAA3" : "#eab308")
            .style("opacity", 0.3)
            .style("animation", `pulse ${2 / animationSpeed[0]}s infinite`)
        }

        // Main marker
        const markerSize = partner.tier === "premium" ? 12 : partner.tier === "standard" ? 10 : 8
        marker
          .append("circle")
          .attr("cx", x)
          .attr("cy", y)
          .attr("r", markerSize)
          .style("fill", partner.streamlnkIntegrated ? "#52AAA3" : "#eab308")
          .style("stroke", "#fff")
          .style("stroke-width", "2px")
          .style("filter", hoveredPartner === partner.id ? "brightness(1.3)" : "none")
          .style("transition", "all 0.3s ease")

        // Tier indicator
        const tierColor = partner.tier === "premium" ? "#10b981" : partner.tier === "standard" ? "#3b82f6" : "#6b7280"
        marker
          .append("circle")
          .attr("cx", x + 8)
          .attr("cy", y - 8)
          .attr("r", 4)
          .style("fill", tierColor)
          .style("stroke", "#fff")
          .style("stroke-width", "1px")

        // Partner name label
        if (hoveredPartner === partner.id || selectedPartner?.id === partner.id) {
          marker
            .append("rect")
            .attr("x", x - 40)
            .attr("y", y - 35)
            .attr("width", 80)
            .attr("height", 20)
            .attr("rx", 10)
            .style("fill", "rgba(255, 255, 255, 0.95)")
            .style("stroke", "#52AAA3")
            .style("stroke-width", "1px")

          marker
            .append("text")
            .attr("x", x)
            .attr("y", y - 22)
            .attr("text-anchor", "middle")
            .style("fill", "#1e293b")
            .style("font-size", "10px")
            .style("font-weight", "600")
            .text(partner.name.length > 15 ? partner.name.substring(0, 15) + "..." : partner.name)
        }
      })
    }
  }, [
    worldData,
    pathGenerator,
    currentZoom,
    hoveredPartner,
    selectedPartner,
    hoveredRoute,
    selectedRoute,
    selectedAsset,
    showRoutes,
    showAssets,
    showHeatmap,
    showEvents,
    showRegionalStats,
    animationSpeed,
    filteredPartners,
    filteredRoutes,
    filteredAssets,
    realTimeData,
    createArc,
    projection,
  ])

  const handleRegionClick = (region: Region) => {
    setSelectedRegion(region.id)

    if (transformRef.current) {
      const [centerX, centerY] = projection(region.center) || [0, 0]
      const scale = region.zoom

      const x = width / 2 - centerX * scale
      const y = height / 2 - centerY * scale

      transformRef.current.setTransform(x, y, scale, 500)
      setCurrentZoom(scale)
    }
  }

  const toggleService = (serviceId: string) => {
    const newActiveServices = new Set(activeServices)
    if (newActiveServices.has(serviceId)) {
      newActiveServices.delete(serviceId)
    } else {
      newActiveServices.add(serviceId)
    }
    setActiveServices(newActiveServices)
  }

  const toggleAllServices = () => {
    if (activeServices.size === serviceTypes.length) {
      setActiveServices(new Set())
    } else {
      setActiveServices(new Set(serviceTypes.map((s) => s.id)))
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#EEF4EE] flex items-center justify-center">
        <div className="text-center">
          <Globe className="w-12 h-12 text-[#52AAA3] animate-spin mx-auto mb-4" />
          <p className="text-slate-700 text-lg">Loading world map data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#EEF4EE] flex flex-col">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#EEF4EE] via-slate-50 to-[#EEF4EE]" />

      {/* Pulse animation styles */}
      <style jsx>{`
        @keyframes pulse {
          0%, 100% { opacity: 0.6; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.1); }
        }
      `}</style>

      {/* Header - Full Width */}
      <header className="relative z-10 p-6 border-b border-slate-200 bg-white/80 backdrop-blur-sm">
        <MapHeader
          selectedPartner={selectedPartner}
          selectedRoute={selectedRoute}
          selectedAsset={selectedAsset}
          isRealTimeEnabled={isRealTimeEnabled}
        />
      </header>

      {/* Main Content Grid */}
      <div
        className="flex-1 grid relative z-10"
        style={{
          gridTemplateColumns: `${isLeftSidebarVisible ? "300px" : "0"} 1fr ${isRealTimeControlsMinimized ? "0" : "300px"}`,
        }}
      >
        {/* Left Sidebar */}
        {isLeftSidebarVisible && (
          <div className="bg-white/80 backdrop-blur-sm border-r border-slate-200 overflow-y-auto max-h-[calc(100vh-120px)]">
            <div className="space-y-0">
              {/* Regional Navigation */}
              {!isRegionalNavMinimized && (
                <div className="border-b border-slate-200">
                  <RegionalNavigation
                    selectedRegion={selectedRegion}
                    activeServices={activeServices}
                    regionalStats={realTimeData.regionalStats}
                    isMinimized={isRegionalNavMinimized}
                    onRegionClick={handleRegionClick}
                    onToggleMinimized={() => setIsRegionalNavMinimized(!isRegionalNavMinimized)}
                    hasSelectedPanels={false}
                  />
                </div>
              )}

              {/* Live Events Panel */}
              {!isLiveEventsMinimized && (
                <div className="border-b border-slate-200">
                  <LiveEventsPanel
                    events={realTimeData.events}
                    showEvents={showEvents}
                    isMinimized={isLiveEventsMinimized}
                    onToggleMinimized={() => setIsLiveEventsMinimized(!isLiveEventsMinimized)}
                  />
                </div>
              )}

              {/* Status Panel */}
              {!isStatusPanelMinimized && (
                <div>
                  <StatusPanel
                    activeServices={activeServices}
                    showRoutes={showRoutes}
                    showAssets={showAssets}
                    filteredAssets={filteredAssets}
                    routeIntensityFilter={routeIntensityFilter}
                    currentZoom={currentZoom}
                    isRealTimeEnabled={isRealTimeEnabled}
                    isMinimized={isStatusPanelMinimized}
                    onToggleMinimized={() => setIsStatusPanelMinimized(!isStatusPanelMinimized)}
                    hasSelectedPanels={false}
                  />
                </div>
              )}

              {/* Minimized Left Sidebar Controls */}
              {(isRegionalNavMinimized || isLiveEventsMinimized || isStatusPanelMinimized) && (
                <div className="p-2 border-b border-slate-200 bg-slate-50">
                  <div className="flex flex-col gap-1">
                    {isRegionalNavMinimized && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsRegionalNavMinimized(false)}
                        className="justify-start text-xs text-slate-600 hover:text-slate-900"
                      >
                        📍 Regions
                      </Button>
                    )}
                    {isLiveEventsMinimized && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsLiveEventsMinimized(false)}
                        className="justify-start text-xs text-slate-600 hover:text-slate-900"
                      >
                        🚨 Events ({realTimeData.events.length})
                      </Button>
                    )}
                    {isStatusPanelMinimized && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsStatusPanelMinimized(false)}
                        className="justify-start text-xs text-slate-600 hover:text-slate-900"
                      >
                        📊 Status
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Floating Left Sidebar Toggle (when completely hidden) */}
        {!isLeftSidebarVisible && (
          <div className="fixed left-4 top-1/2 transform -translate-y-1/2 z-30">
            <div className="bg-white/90 backdrop-blur-sm border border-slate-200 rounded-lg shadow-lg p-2">
              <div className="flex flex-col gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsRegionalNavMinimized(false)}
                  className="justify-start text-xs text-slate-600 hover:text-slate-900 w-20"
                >
                  📍
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsLiveEventsMinimized(false)}
                  className="justify-start text-xs text-slate-600 hover:text-slate-900 w-20"
                >
                  🚨
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsStatusPanelMinimized(false)}
                  className="justify-start text-xs text-slate-600 hover:text-slate-900 w-20"
                >
                  📊
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Center Map Area */}
        <div className="bg-white">
          <div className="h-[calc(100vh-120px)] relative">
            <TransformWrapper
              ref={transformRef}
              initialScale={1}
              minScale={0.5}
              maxScale={8}
              centerOnInit={true}
              wheel={{ step: 0.1 }}
              pinch={{ step: 5 }}
              doubleClick={{ disabled: false }}
              onTransformed={(ref) => {
                setCurrentZoom(ref.state.scale)
              }}
            >
              {({ zoomIn, zoomOut, resetTransform }) => (
                <>
                  {/* Map Controls */}
                  <MapControls
                    showRoutes={showRoutes}
                    showAssets={showAssets}
                    onZoomIn={zoomIn}
                    onZoomOut={zoomOut}
                    onReset={() => {
                      resetTransform()
                      setSelectedRegion(null)
                      setCurrentZoom(1)
                    }}
                    onToggleRoutes={() => setShowRoutes(!showRoutes)}
                    onToggleAssets={() => setShowAssets(!showAssets)}
                  />

                  {/* Map SVG */}
                  <TransformComponent
                    wrapperClass="w-full h-full flex items-center justify-center"
                    contentClass="cursor-grab active:cursor-grabbing"
                  >
                    <svg
                      ref={svgRef}
                      width={width}
                      height={height}
                      className="bg-white/50"
                      style={{ maxWidth: "100%", height: "auto" }}
                    />
                  </TransformComponent>
                </>
              )}
            </TransformWrapper>
          </div>
        </div>

        {/* Right Sidebar */}
        {!isRealTimeControlsMinimized && (
          <div className="bg-white/80 backdrop-blur-sm border-l border-slate-200 overflow-y-auto max-h-[calc(100vh-120px)]">
            <RealTimeControls
              isRealTimeEnabled={isRealTimeEnabled}
              showAssets={showAssets}
              showHeatmap={showHeatmap}
              showEvents={showEvents}
              showRegionalStats={showRegionalStats}
              updateInterval={updateInterval}
              activeServices={activeServices}
              realTimeData={realTimeData}
              isMinimized={isRealTimeControlsMinimized}
              onToggleRealTime={() => setIsRealTimeEnabled(!isRealTimeEnabled)}
              onToggleAssets={setShowAssets}
              onToggleHeatmap={setShowHeatmap}
              onToggleEvents={setShowEvents}
              onToggleRegionalStats={setShowRegionalStats}
              onUpdateIntervalChange={setUpdateInterval}
              onToggleService={toggleService}
              onToggleAllServices={toggleAllServices}
              onToggleMinimized={() => setIsRealTimeControlsMinimized(!isRealTimeControlsMinimized)}
            />
          </div>
        )}

        {/* Floating Right Sidebar Toggle (when hidden) */}
        {isRealTimeControlsMinimized && (
          <div className="fixed right-4 top-1/2 transform -translate-y-1/2 z-30">
            <div className="bg-white/90 backdrop-blur-sm border border-slate-200 rounded-lg shadow-lg p-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsRealTimeControlsMinimized(false)}
                className="text-slate-600 hover:text-slate-900"
              >
                ⚙️
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Overlay Panels for Selected Items */}
      {selectedAsset && (
        <div className="fixed inset-0 z-50 bg-black/20 backdrop-blur-sm flex items-center justify-center">
          <div className="w-96 max-h-[80vh] overflow-y-auto">
            <AssetPanel selectedAsset={selectedAsset} onClose={() => setSelectedAsset(null)} />
          </div>
        </div>
      )}

      {selectedRoute && !selectedAsset && (
        <div className="fixed inset-0 z-50 bg-black/20 backdrop-blur-sm flex items-center justify-center">
          <div className="w-96 max-h-[80vh] overflow-y-auto">
            <RoutePanel
              selectedRoute={selectedRoute}
              filteredAssets={filteredAssets}
              onClose={() => setSelectedRoute(null)}
              onSelectAsset={setSelectedAsset}
            />
          </div>
        </div>
      )}

      {selectedPartner && !selectedRoute && !selectedAsset && (
        <div className="fixed inset-0 z-50 bg-black/20 backdrop-blur-sm flex items-center justify-center">
          <div className="w-96 max-h-[80vh] overflow-y-auto">
            <PartnerPanel
              selectedPartner={selectedPartner}
              filteredAssets={filteredAssets}
              onClose={() => setSelectedPartner(null)}
            />
          </div>
        </div>
      )}
    </div>
  )
}
