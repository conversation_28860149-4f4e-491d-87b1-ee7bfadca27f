"use client"
import { partners, routes } from "@/data/map-data"
import type { LiveAsset } from "@/types/map-types"
import { X } from "lucide-react"
import { Button } from "@/components/ui/button"

interface StatusPanelProps {
  activeServices: Set<string>
  showRoutes: boolean
  showAssets: boolean
  filteredAssets: LiveAsset[]
  routeIntensityFilter: string[]
  currentZoom: number
  isRealTimeEnabled: boolean
  isMinimized: boolean
  onToggleMinimized: () => void
  hasSelectedPanels: boolean
}

export function StatusPanel({
  activeServices,
  showRoutes,
  showAssets,
  filteredAssets,
  routeIntensityFilter,
  currentZoom,
  isRealTimeEnabled,
  isMinimized,
  onToggleMinimized,
  hasSelectedPanels,
}: StatusPanelProps) {
  const filteredPartners = partners.filter((partner) => partner.services.some((service) => activeServices.has(service)))

  const integratedPartners = filteredPartners.filter((p) => p.streamlnkIntegrated)

  const filteredRoutes = routes.filter(
    (route) =>
      route.services.some((service) => activeServices.has(service)) && routeIntensityFilter.includes(route.intensity),
  )

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-slate-900 font-semibold">System Status</h3>
        <Button variant="ghost" size="sm" onClick={onToggleMinimized} className="text-slate-600 hover:text-slate-900">
          <X className="w-4 h-4" />
        </Button>
      </div>

      <div className={`flex flex-col gap-3 text-sm ${isMinimized ? "gap-2" : ""}`}>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-[#52AAA3] rounded-full" />
          <span className="text-slate-900">{filteredPartners.length} Partners</span>
        </div>
        {!isMinimized && (
          <>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-600 rounded-full" />
              <span className="text-slate-900">{integratedPartners.length} Integrated</span>
            </div>
            {showRoutes && (
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-600 rounded-full" />
                <span className="text-slate-900">{filteredRoutes.length} Routes</span>
              </div>
            )}
            {showAssets && (
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-teal-600 rounded-full" />
                <span className="text-slate-900">{filteredAssets.length} Live Assets</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-slate-600 rounded-full" />
              <span className="text-slate-900">Zoom: {currentZoom.toFixed(1)}x</span>
            </div>
          </>
        )}
        <div className="flex items-center gap-2">
          {isRealTimeEnabled ? (
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
          ) : (
            <div className="w-3 h-3 bg-red-500 rounded-full" />
          )}
          <span className="text-slate-900">{isRealTimeEnabled ? "Live" : "Offline"}</span>
        </div>
      </div>
    </div>
  )
}
