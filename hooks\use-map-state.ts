"use client"

import { useState, useMemo } from "react"
import { serviceTypes, partners, routes } from "@/data/map-data"
import type { Partner, Route, LiveAsset, Region } from "@/types/map-types"

export function useMapState() {
  // Core state
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null)
  const [selectedRegion, setSelectedRegion] = useState<string | null>(null)
  const [selectedPartner, setSelectedPartner] = useState<Partner | null>(null)
  const [selectedRoute, setSelectedRoute] = useState<Route | null>(null)
  const [selectedAsset, setSelectedAsset] = useState<LiveAsset | null>(null)
  const [currentZoom, setCurrentZoom] = useState(1)
  const [hoveredPartner, setHoveredPartner] = useState<string | null>(null)
  const [hoveredRoute, setHoveredRoute] = useState<string | null>(null)

  // Filter state
  const [activeServices, setActiveServices] = useState<Set<string>>(new Set(serviceTypes.map((s) => s.id)))
  const [showRoutes, setShowRoutes] = useState(true)
  const [routeIntensityFilter, setRouteIntensityFilter] = useState<string[]>(["high", "medium", "low"])
  const [animationSpeed, setAnimationSpeed] = useState([1])

  // Real-time controls
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true)
  const [showAssets, setShowAssets] = useState(true)
  const [showHeatmap, setShowHeatmap] = useState(true)
  const [showEvents, setShowEvents] = useState(true)
  const [showRegionalStats, setShowRegionalStats] = useState(true)
  const [updateInterval, setUpdateInterval] = useState([2000])

  // Sidebar minimization state
  const [isRealTimeControlsMinimized, setIsRealTimeControlsMinimized] = useState(false)
  const [isRegionalNavMinimized, setIsRegionalNavMinimized] = useState(false)
  const [isLiveEventsMinimized, setIsLiveEventsMinimized] = useState(false)
  const [isStatusPanelMinimized, setIsStatusPanelMinimized] = useState(false)

  // Calculate if left sidebar should be shown
  const isLeftSidebarVisible = !isRegionalNavMinimized || !isLiveEventsMinimized || !isStatusPanelMinimized

  // Service management functions
  const toggleService = (serviceId: string) => {
    const newActiveServices = new Set(activeServices)
    if (newActiveServices.has(serviceId)) {
      newActiveServices.delete(serviceId)
    } else {
      newActiveServices.add(serviceId)
    }
    setActiveServices(newActiveServices)
  }

  const toggleAllServices = () => {
    if (activeServices.size === serviceTypes.length) {
      setActiveServices(new Set())
    } else {
      setActiveServices(new Set(serviceTypes.map((s) => s.id)))
    }
  }

  return {
    // Core state
    selectedCountry,
    setSelectedCountry,
    selectedRegion,
    setSelectedRegion,
    selectedPartner,
    setSelectedPartner,
    selectedRoute,
    setSelectedRoute,
    selectedAsset,
    setSelectedAsset,
    currentZoom,
    setCurrentZoom,
    hoveredPartner,
    setHoveredPartner,
    hoveredRoute,
    setHoveredRoute,

    // Filter state
    activeServices,
    setActiveServices,
    showRoutes,
    setShowRoutes,
    routeIntensityFilter,
    setRouteIntensityFilter,
    animationSpeed,
    setAnimationSpeed,

    // Real-time controls
    isRealTimeEnabled,
    setIsRealTimeEnabled,
    showAssets,
    setShowAssets,
    showHeatmap,
    setShowHeatmap,
    showEvents,
    setShowEvents,
    showRegionalStats,
    setShowRegionalStats,
    updateInterval,
    setUpdateInterval,

    // Sidebar state
    isRealTimeControlsMinimized,
    setIsRealTimeControlsMinimized,
    isRegionalNavMinimized,
    setIsRegionalNavMinimized,
    isLiveEventsMinimized,
    setIsLiveEventsMinimized,
    isStatusPanelMinimized,
    setIsStatusPanelMinimized,
    isLeftSidebarVisible,

    // Functions
    toggleService,
    toggleAllServices,
  }
}
