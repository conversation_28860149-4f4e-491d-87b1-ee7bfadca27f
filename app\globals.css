@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 238 244 238;
    --foreground: 15 23 42;
    --card: 255 255 255;
    --card-foreground: 15 23 42;
    --popover: 255 255 255;
    --popover-foreground: 15 23 42;
    --primary: 82 170 163;
    --primary-foreground: 255 255 255;
    --secondary: 241 245 249;
    --secondary-foreground: 15 23 42;
    --muted: 241 245 249;
    --muted-foreground: 100 116 139;
    --accent: 241 245 249;
    --accent-foreground: 15 23 42;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 226 232 240;
    --input: 226 232 240;
    --ring: 82 170 163;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 15 23 42;
    --foreground: 241 245 249;
    --card: 15 23 42;
    --card-foreground: 241 245 249;
    --popover: 15 23 42;
    --popover-foreground: 241 245 249;
    --primary: 93 193 185;
    --primary-foreground: 15 23 42;
    --secondary: 30 41 59;
    --secondary-foreground: 241 245 249;
    --muted: 30 41 59;
    --muted-foreground: 148 163 184;
    --accent: 30 41 59;
    --accent-foreground: 241 245 249;
    --destructive: 220 38 38;
    --destructive-foreground: 241 245 249;
    --border: 30 41 59;
    --input: 30 41 59;
    --ring: 93 193 185;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom glassmorphism styles for light theme */
.glassmorphism {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(82, 170, 163, 0.2);
}

/* Map specific styles */
.country {
  transition: all 0.3s ease;
}

.country:hover {
  filter: brightness(0.9);
}

/* Smooth zoom transitions */
.react-transform-wrapper {
  width: 100% !important;
  height: 100% !important;
}

.react-transform-component {
  width: 100% !important;
  height: 100% !important;
}

/* Animation keyframes */
@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

.animate-pulse-custom {
  animation: pulse 2s infinite;
}
